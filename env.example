# Ethereum RPC Configuration
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/********************************
ETHEREUM_WS_URL=wss://mainnet.infura.io/v3/********************************
START_BLOCK_NUMBER=22759500

# MongoDB Configuration
MONGO_URI=mongodb+srv://haitranwang:<EMAIL>/
MONGO_DATABASE=ethereum_raw_data
MONGO_CONNECT_TIMEOUT=10s
MONGO_MAX_POOL_SIZE=100

# Application Configuration
APP_PORT=8080
APP_ENV=development
LOG_LEVEL=info

# Crawler Configuration - Very conservative
BATCH_SIZE=1
CONCURRENT_WORKERS=1
RETRY_ATTEMPTS=3
RETRY_DELAY=2s

# Batch Upsert Configuration
CRAWLER_USE_UPSERT=true
CRAWLER_UPSERT_FALLBACK=true

# GraphQL Configuration
GRAPHQL_ENDPOINT=/graphql
GRAPHQL_PLAYGROUND=true

# Monitoring Configuration
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=30s
